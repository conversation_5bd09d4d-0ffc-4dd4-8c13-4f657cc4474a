# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Unity specific ignores
Library/
ProjectSettings/
Temp/
UserSettings/
Packages/
.plastic/
.roo/
.taskmaster/

# Unity generated files
*.csproj
*.sln
*.unityproj
*.pidb
*.userprefs
*.tmp
*.swp
*.swo

# Build outputs
Build/
Builds/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Task files
# tasks.json
# tasks/ 
