# Unity 开发规则 (Shrimp Rules)

## 🚫 关键规则 (绝对不能违反)

### 内存管理
- **禁止** 手动管理 `UnityEngine.Object` 派生类的内存
- **禁止** 对 `MonoBehaviour` 或 `ScriptableObject` 使用 `new` 关键字，应使用 `AddComponent<>()` 或 `CreateInstance<>()`
- **禁止** 从非主线程访问大多数 Unity API
- **必须** 关注垃圾回收(GC)的影响，分析并优化内存分配

### 架构违规
- **禁止** 在不考虑 Unity 的 `ObjectPool` 的情况下创建自定义对象池
- **禁止** 在有超过1000个相似对象时不使用 DOTS/ECS（除非有充分理由）
- **禁止** 硬编码配置数据（应使用 `ScriptableObject`）

### 性能违规
- **禁止** 在 `Update()`、`LateUpdate()` 或 `FixedUpdate()` 中进行大量计算
- **禁止** 在性能关键代码中同步加载资源：`Resources.Load()`
- **禁止** 长时间阻塞主线程

## ✅ 强制实践 (必须执行)

### 实现前检查清单
1. **必须** 先查阅 Unity 官方 API 文档
2. **必须** 搜索项目中可复用的现有组件
3. **必须** 检查项目中可复用的现有资源
4. **必须** 验证 DOTS/ECS 是否更适合该用例

### 代码结构要求
- **必须** 每个类只负责单一职责
- **必须** 遵循 SOLID 原则
- **必须** 使用命名空间组织代码
- **必须** 规范 `using` 语句的顺序

### 系统集成规则
根据场景选择合适的系统：
- 创建 >1000 个对象 → 使用 DOTS/ECS + 对象池
- 频繁访问数据 → 使用缓存机制或 `ScriptableObject`
- 高 CPU 使用 → 使用 Job System 和 Burst Compiler
- 高内存使用 → 使用 Memory Profiler 和优化技术
- UI 数据绑定 → 考虑 MVVM 或 MVC 架构
- 复杂操作 → 使用 Command Pattern
- 松耦合 → 使用事件 (C# events, `UnityEvent`)
- 配置数据 → 使用 `ScriptableObject`

## 📊 首选方法 (优先级顺序)

1. Unity 原生功能 > 自定义实现
2. DOTS/ECS > 传统的 `GameObject`/`MonoBehaviour` 方式（性能关键系统）
3. Job System/Burst Compiler > 在 `MonoBehaviour` 中进行大量 CPU 任务
4. Unity 的对象池系统 > 自定义对象池系统
5. 数据驱动配置 (`ScriptableObject`) > 硬编码值
6. 增量更新 > 完全重写
7. 批量操作 > 逐个处理
8. 异步操作 (`Async/Await`, 协程) > 同步阻塞

## 🔍 验证清单

### 代码检查
- [ ] 没有直接实例化 `MonoBehaviour`
- [ ] 大规模系统已考虑 DOTS/ECS
- [ ] 使用了 Unity 的性能分析工具
- [ ] 优先使用 Unity 的功能
- [ ] 保持单一职责
- [ ] 使用数据驱动配置

### 性能要求
- 帧率：目标平台最低 60 FPS
- 主线程：无长时间阻塞
- 内存：在分配预算内，最小化 GC 峰值

---

**⚠️ 违反规则 = 代码将被立即拒绝**